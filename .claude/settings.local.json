{"permissions": {"allow": ["WebFetch(domain:modelcontextprotocol.io)", "<PERSON><PERSON>(mkdir:*)", "Bash(nimble test:*)", "Bash(nim c:*)", "Bash(nim --version)", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(mv:*)", "Bash(./tests/test_basic)", "mcp__ide__getDiagnostics", "Bash(grep:*)", "<PERSON><PERSON>(timeout:*)", "Bash(find:*)", "Bash(rg:*)", "Bash(./test_handshake.sh:*)", "Bash(rm:*)", "mcp__calculator_server__power", "mcp__calculator_server__multiply", "mcp__macro_calculator__factorial", "mcp__macro_calculator__compare", "Bash(nimble install:*)", "mcp__macro_mummy_calculator__factorial", "<PERSON><PERSON>(chmod:*)", "Bash(ls:*)", "Bash(./examples/test_calculators.sh:*)", "WebFetch(domain:github.com)", "Bash(bash:*)", "WebFetch(domain:docs.anthropic.com)", "Bash(cp:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(-print)", "Bash(for file in src/nimcp/*.nim)", "Bash(do echo \"=== $file ===\")", "Bash(awk:*)", "Bash(done)"], "deny": []}}